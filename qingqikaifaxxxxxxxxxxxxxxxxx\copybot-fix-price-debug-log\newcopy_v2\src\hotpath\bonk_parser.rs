use crate::shared::types::{HotPathTrade, TradeType};
use memchr::memchr;
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use spl_associated_token_account::get_associated_token_address;
use uuid::Uuid;
use tracing::{debug, warn};

/// Bonk交易数据结构
#[derive(Debug, Clone)]
pub struct BonkTrade {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
}

impl BonkTrade {
    /// 转换为HotPathTrade格式，用于统一处理
    pub fn to_hotpath_trade(&self, user_wallet_pubkey: &Pubkey) -> Option<HotPathTrade> {
        // 解析mint地址
        let mint_pubkey = Pubkey::from_str(&self.mint_address).ok()?;
        
        // 解析创作者金库地址（使用pool_base_vault作为创作者金库）
        let creator_vault_pubkey = Pubkey::from_str(&self.pool_base_vault).ok()?;
        
        // 确定交易类型
        let trade_type = match self.trade_direction.as_str() {
            "Buy" => TradeType::Buy,
            "Sell" => TradeType::Sell,
            _ => TradeType::Unknown,
        };
        
        // 计算SOL成本和代币数量
        let (sol_cost, token_amount) = match trade_type {
            TradeType::Buy => {
                // 买入：amount_in是SOL，amount_out是代币
                let sol_cost = self.amount_in as f64 / 1_000_000_000.0; // lamports转SOL
                let token_amount = self.amount_out; // 代币数量（最小单位）
                (sol_cost, token_amount)
            },
            TradeType::Sell => {
                // 卖出：amount_in是代币，amount_out是SOL
                let sol_cost = self.amount_out as f64 / 1_000_000_000.0; // lamports转SOL
                let token_amount = self.amount_in; // 代币数量（最小单位）
                (sol_cost, token_amount)
            },
            _ => return None,
        };
        
        // 预计算账户地址（Bonk使用Raydium协议，需要不同的PDA计算）
        // 注意：这里使用简化的计算，实际可能需要根据Raydium协议调整
        let bonding_curve_pubkey = creator_vault_pubkey; // 简化处理
        let associated_bonding_curve = get_associated_token_address(&bonding_curve_pubkey, &mint_pubkey);
        let user_ata = get_associated_token_address(user_wallet_pubkey, &mint_pubkey);
        
        Some(HotPathTrade {
            trade_id: Uuid::new_v4().to_string(),
            trade_type,
            signature: self.signature.clone(),
            sol_cost,
            token_amount,
            signer: self.signer.clone(),
            price: self.price_after,
            mint_pubkey,
            creator_vault_pubkey,
            bonding_curve_pubkey,
            associated_bonding_curve,
            user_ata,
            slippage_bps: 0, // 将在后续设置
        })
    }
}

/// 解析Bonk Redis数据格式
pub fn parse_bonk_from_redis_bytes(payload: &[u8]) -> Vec<BonkTrade> {
    let mut trades = Vec::new();
    
    // 将字节转换为字符串
    let data_str = match std::str::from_utf8(payload) {
        Ok(s) => s,
        Err(e) => {
            debug!("Bonk数据UTF-8解析失败: {}", e);
            return trades;
        }
    };
    
    // 解析Bonk数据格式（类似JSON但不是标准JSON）
    if let Some(bonk_trade) = parse_single_bonk_trade(data_str) {
        trades.push(bonk_trade);
    }
    
    trades
}

/// 解析单个Bonk交易数据
fn parse_single_bonk_trade(data: &str) -> Option<BonkTrade> {
    let mut trade = BonkTrade {
        signature: String::new(),
        pool_state: String::new(),
        signer: String::new(),
        mint_address: String::new(),
        total_base_sell: 0,
        virtual_base: 0,
        virtual_quote: 0,
        real_base_before: 0,
        real_quote_before: 0,
        real_base_after: 0,
        real_quote_after: 0,
        amount_in: 0,
        amount_out: 0,
        protocol_fee: 0,
        platform_fee: 0,
        share_fee: 0,
        trade_direction: String::new(),
        pool_status: String::new(),
        price_before: 0.0,
        price_after: 0.0,
        slippage: 0.0,
        pool_base_vault: String::new(),
        pool_quote_vault: String::new(),
    };
    
    // 解析每个字段
    for line in data.lines() {
        let line = line.trim();
        if let Some(pos) = line.find("\": ") {
            let key = &line[..pos].trim_start_matches('"');
            let value_start = pos + 3;
            if value_start >= line.len() { continue; }
            
            let value = &line[value_start..];
            let value = value.trim_end_matches(',').trim_end_matches('"').trim_start_matches('"');
            
            match key {
                "signature" => trade.signature = value.to_string(),
                "pool_state" => trade.pool_state = value.to_string(),
                "signer" => trade.signer = value.to_string(),
                "mint_address" => trade.mint_address = value.to_string(),
                "total_base_sell" => trade.total_base_sell = value.parse().unwrap_or(0),
                "virtual_base" => trade.virtual_base = value.parse().unwrap_or(0),
                "virtual_quote" => trade.virtual_quote = value.parse().unwrap_or(0),
                "real_base_before" => trade.real_base_before = value.parse().unwrap_or(0),
                "real_quote_before" => trade.real_quote_before = value.parse().unwrap_or(0),
                "real_base_after" => trade.real_base_after = value.parse().unwrap_or(0),
                "real_quote_after" => trade.real_quote_after = value.parse().unwrap_or(0),
                "amount_in" => trade.amount_in = value.parse().unwrap_or(0),
                "amount_out" => trade.amount_out = value.parse().unwrap_or(0),
                "protocol_fee" => trade.protocol_fee = value.parse().unwrap_or(0),
                "platform_fee" => trade.platform_fee = value.parse().unwrap_or(0),
                "share_fee" => trade.share_fee = value.parse().unwrap_or(0),
                "trade_direction" => trade.trade_direction = value.to_string(),
                "pool_status" => trade.pool_status = value.to_string(),
                "price_before" => trade.price_before = value.parse().unwrap_or(0.0),
                "price_after" => trade.price_after = value.parse().unwrap_or(0.0),
                "slippage" => trade.slippage = value.parse().unwrap_or(0.0),
                "pool_base_vault" => trade.pool_base_vault = value.to_string(),
                "pool_quote_vault" => trade.pool_quote_vault = value.to_string(),
                _ => {}
            }
        }
    }
    
    // 验证必要字段
    if trade.signature.is_empty() || trade.mint_address.is_empty() || trade.signer.is_empty() {
        debug!("Bonk交易数据缺少必要字段");
        return None;
    }
    
    Some(trade)
}

/// 将Bonk交易转换为HotPathTrade列表
pub fn parse_bonk_trades_from_redis_bytes(payload: &[u8], user_wallet_pubkey: &Pubkey) -> Vec<HotPathTrade> {
    let bonk_trades = parse_bonk_from_redis_bytes(payload);
    let mut hot_path_trades = Vec::new();
    
    for bonk_trade in bonk_trades {
        if let Some(hot_path_trade) = bonk_trade.to_hotpath_trade(user_wallet_pubkey) {
            hot_path_trades.push(hot_path_trade);
        } else {
            debug!("Bonk交易转换为HotPathTrade失败: {}", bonk_trade.signature);
        }
    }
    
    hot_path_trades
}
