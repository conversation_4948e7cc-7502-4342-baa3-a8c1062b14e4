use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

/// 跟单模式枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FollowMode {
    /// 百分比跟单（原有模式）
    Percentage,
    /// 固定金额跟单（新模式）
    FixedAmount,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TradeType {
    Buy,
    Sell,
    Unknown,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TipProvider {
    Jito,
    // 其他提供商可以稍后添加
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TipConfig {
    pub provider: TipProvider,
    pub percentage: f64,
}

/// 包含所有热路径所需信息，包括预计算的账户地址。
#[derive(Debug, Clone)]
pub struct HotPathTrade {
    // --- 新增唯一ID ---
    pub trade_id: String,

    // --- 原始信息 (部分保留用于日志) ---
    pub trade_type: TradeType,
    pub signature: String,
    pub sol_cost: f64,
    pub token_amount: u64,
    pub signer: String, // 跟随钱包的地址 (字符串)
    pub price: f64,

    // --- 预计算账户 (性能优化的核心) ---
    pub mint_pubkey: Pubkey,
    pub creator_vault_pubkey: Pubkey,
    pub bonding_curve_pubkey: Pubkey,
    pub associated_bonding_curve: Pubkey,
    pub user_ata: Pubkey,

    // --- 其他信息 ---
    pub slippage_bps: u64,
}

/// 定义单个钱包或策略的静态配置参数，直接映射自JSON配置。
/// 这些参数在交易周期开始前加载，并在热路径计算中作为只读数据使用。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WalletConfig {
    /// 钱包地址，作为HashMap的Key，但在结构体中也保留一份以方便使用。
    pub wallet_address: String,
    
    /// 此配置是否激活。
    pub is_active: bool,
    
    /// 钱包备注名称（可选）
    pub remark: Option<String>,
    
    /// 跟单模式：百分比跟单或固定金额跟单
    #[serde(default = "default_follow_mode")]
    pub follow_mode: FollowMode,

    /// 跟单金额百分比，相对于被跟随者的交易额（百分比模式使用）。
    pub follow_percentage: Option<f64>,

    /// 固定跟单金额（SOL），固定金额模式使用。
    pub fixed_follow_amount_sol: Option<f64>,
    
    /// 购买金额的绝对值下限（SOL）。
    pub sol_amount_min: Option<f64>,
    
    /// 购买金额的绝对值上限（SOL）。
    pub sol_amount_max: Option<f64>,

    /// 价格检查：可接受的最低价格。如果交易价格低于此值，则不跟单。
    pub min_price_multiplier: Option<f64>,

    /// 价格检查：可接受的最高价格。如果交易价格高于此值，则不跟单。
    pub max_price_multiplier: Option<f64>,
    
    /// 滑点容忍度，以百分比表示 (例如, 2.0 表示 2%)。
    pub slippage_percentage: f64,
    
    /// Solana交易的优先费用，单位是微拉姆波特（microlamports）。
    pub priority_fee: u64,
    
    /// Solana交易的计算单元限制。
    pub compute_unit_limit: u32,

    /// 止盈启动百分比 (如 50.0 表示 +50%)
    pub take_profit_start_pct: Option<f64>,
    /// 止盈间隔百分比 (如 30.0 表示每 +30%)
    pub take_profit_step_pct: Option<f64>,
    /// 每次止盈卖出仓位百分比 (如 10.0 表示卖出10%)
    pub take_profit_sell_portion_pct: Option<f64>,

    // --- 新增：可选择的止盈策略 ---
    /// 止盈策略选择: "standard" (默认), "trailing", "exponential", "volatility"
    pub take_profit_strategy: Option<String>,

    // --- "trailing" 策略参数 ---
    /// 追踪止盈回撤百分比 (例如 15.0 表示从最高点回撤15%时卖出)
    pub trailing_stop_profit_percentage: Option<f64>,

    // --- "exponential" 策略参数 (已根据新逻辑重构) ---
    /// 指数卖出策略的盈利触发间隔 (例如 10.0 表示每涨10%触发一次)
    pub exponential_sell_trigger_step_pct: Option<f64>,
    /// 指数卖出的基础卖出比例 (例如 5.0 表示基础份额为5%)
    pub exponential_sell_base_portion_pct: Option<f64>,
    /// 指数卖出的幂 (例如 2.0 代表平方, 3.0 代表立方)
    pub exponential_sell_power: Option<f64>,

    // --- "volatility" 策略参数 ---
    /// 布林带窗口大小（价格事件数）
    pub volatility_bb_window_size: Option<usize>,
    /// 布林带标准差倍数 (例如 1.8)
    pub volatility_bb_stddev: Option<f64>,
    /// ATR样本数 (例如 100)
    pub volatility_atr_samples: Option<usize>,
    /// ATR突增阈值倍数 (例如 1.3)
    pub volatility_atr_multiplier: Option<f64>,
    /// 波动率策略卖出比例 (例如 40.0 表示卖出40%)
    pub volatility_sell_percent: Option<f64>,
    /// 波动率策略冷却时间（毫秒）
    pub volatility_cooldown_ms: Option<u64>,

    /// 止损百分比（卖出逻辑，本次暂不实现）。
    pub stop_loss_percentage: Option<f64>,

    /// 进场确认等待毫秒数，用于跟单买入后在指定毫秒数内无价格变化则触发全部卖出。
    pub entry_confirmation_ms: Option<u64>,

    // --- 动态持仓延长策略参数 ---
    /// 触发涨幅百分比 (如 30.0 表示涨30%)
    pub dynamic_hold_trigger_pct: Option<f64>,
    /// 检查窗口秒数 (例如每2秒检查一次)
    pub dynamic_hold_check_window_secs: Option<u64>,
    /// 每次触发后延长的毫秒数
    pub dynamic_hold_extend_ms: Option<u64>,
    /// 单笔买入允许的最长持仓毫秒数
    pub dynamic_hold_max_ms: Option<u64>,
    /// 硬止损百分比 (亏损达到该百分比立即全部卖出, 如 20.0 表示 -20%)
    pub hard_stop_loss_pct: Option<f64>,
    /// 回调止损百分比 (从波峰回吐该百分比立即卖出, 如 50.0 表示回吐 50%)
    pub callback_stop_pct: Option<f64>,

    /// 加速器小费百分比，例如 0.1 表示买入金额的 0.1% 作为小费
    pub accelerator_tip_percentage: Option<f64>,

    /// (已废弃) 旧版止盈百分比字段，为保持向后兼容保留。
    #[serde(alias = "take_profit_percentage")] // 允许旧配置名称映射
    pub take_profit_percentage_legacy: Option<f64>,

    /// 自动暂停配置
    #[serde(default)]
    pub auto_suspend_config: Option<AutoSuspendConfig>,

    // --- 最小仓位/卖出比例保护 ---
    /// 当计算出的单次卖出比例低于该值，直接清仓而非分批(%). 缺省=0.5 表示0.5%
    pub min_partial_sell_pct: Option<f64>,

    /// 卖出滑点容忍度，以百分比表示，若为空则使用 slippage_percentage
    #[serde(default)]
    pub sell_slippage_percentage: Option<f64>,
    /// 卖出交易的优先费用 (μlamports)。为空则使用 priority_fee
    #[serde(default)]
    pub sell_priority_fee: Option<u64>,
    /// 卖出交易的小费百分比。为空则使用 accelerator_tip_percentage 或 1%
    #[serde(default)]
    pub sell_tip_percentage: Option<f64>,
    /// 卖出交易的计算单元限制。为空则使用 compute_unit_limit
    #[serde(default)]
    pub sell_compute_unit_limit: Option<u32>,

    // --- 卖出重试配置 ---
    /// 卖出失败时的最大重试次数，默认为5次
    #[serde(default = "default_sell_retry_max_attempts")]
    pub sell_retry_max_attempts: u32,
    /// 每次重试增加的滑点百分比，默认为2.0%
    #[serde(default = "default_sell_retry_slippage_increment")]
    pub sell_retry_slippage_increment: f64,

    // --- Bonk协议支持配置 ---
    /// 是否启用Bonk协议跟单，默认为false
    #[serde(default)]
    pub bonk_enabled: bool,
    /// Bonk协议专用滑点容忍度，为空则使用通用slippage_percentage
    #[serde(default)]
    pub bonk_slippage_percentage: Option<f64>,
    /// Bonk协议专用优先费用，为空则使用通用priority_fee
    #[serde(default)]
    pub bonk_priority_fee: Option<u64>,
    /// Bonk协议专用计算单元限制，为空则使用通用compute_unit_limit
    #[serde(default)]
    pub bonk_compute_unit_limit: Option<u32>,
}

/// 交易事件的状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum TradeStatus {
    Pending,
    Confirmed,
    Failed,
    Retrying,
}

/// 用于通过SSE实时推送给客户端的交易事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeEvent {
    /// 交易的唯一ID
    pub trade_id: String,
    /// 交易的当前状态
    pub status: TradeStatus,
    /// 交易类型 (买/卖)
    pub trade_type: TradeType,
    /// 交易签名，在Confirmed或Failed状态下可用
    pub signature: String,
    /// 代币的Mint地址
    pub mint: String,
    /// 交易发生时的时间戳 (Unix Timestamp)
    pub block_time: i64,
    /// 交易发生时的SOL美元价格
    pub sol_price_usd: f64,
    /// [买入] 花费的SOL / [卖出] 获得的SOL
    pub sol_amount: f64,
    /// [买入] 花费的USD / [卖出] 获得的USD
    pub usd_amount: f64,
    /// 涉及的代币数量
    pub token_amount: u64,
    /// 交易者钱包地址
    pub user_wallet: String,
    /// 被跟单的钱包地址
    pub followed_wallet: Option<String>,
    /// [卖出] 交易的利润 (USD)
    pub profit_usd: Option<f64>,
    /// 失败原因，在Failed状态下可用
    pub failure_reason: Option<String>,
}

/// 用于持久化存储的交易历史记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TradeRecord {
    /// 记录的时间戳 (UTC)
    pub timestamp: String,
    /// 触发跟单的钱包地址
    pub followed_wallet: String,
    /// 交易签名
    pub signature: String,
    /// 代币的Mint地址
    pub mint: String,
    /// 买入花费的SOL数量
    pub sol_cost: f64,
    /// 买入的代币数量
    pub token_amount: u64,
    /// 交易状态
    pub status: String,
}

/// 默认跟单模式为百分比跟单（向后兼容）
fn default_follow_mode() -> FollowMode {
    FollowMode::Percentage
}

/// 默认卖出重试最大次数
fn default_sell_retry_max_attempts() -> u32 {
    5
}

/// 默认每次重试增加的滑点百分比
fn default_sell_retry_slippage_increment() -> f64 {
    2.0
}

/// 自动暂停配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoSuspendConfig {
    /// 是否启用自动暂停
    pub enabled: bool,
    /// 滑动窗口大小（最近N笔交易）
    pub window_size: usize,
    /// 亏损笔数阈值
    pub loss_count: usize,
    /// 亏损阈值百分比（如-5.0表示-5%）
    pub loss_threshold: f64,
}

impl Default for AutoSuspendConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            window_size: 5,
            loss_count: 3,
            loss_threshold: -5.0,
        }
    }
}

impl WalletConfig {
    /// 验证跟单配置的一致性
    pub fn validate_follow_config(&self) -> Result<(), String> {
        match self.follow_mode {
            FollowMode::Percentage => {
                if self.follow_percentage.is_none() {
                    return Err("百分比跟单模式下必须设置 follow_percentage".to_string());
                }
                if self.fixed_follow_amount_sol.is_some() {
                    return Err("百分比跟单模式下不应设置 fixed_follow_amount_sol".to_string());
                }
            },
            FollowMode::FixedAmount => {
                if self.fixed_follow_amount_sol.is_none() {
                    return Err("固定金额跟单模式下必须设置 fixed_follow_amount_sol".to_string());
                }
                if self.follow_percentage.is_some() {
                    return Err("固定金额跟单模式下不应设置 follow_percentage".to_string());
                }
            }
        }
        Ok(())
    }

    /// 创建一个规范化的配置，确保只有对应模式的字段有值
    pub fn normalize(mut self) -> Self {
        match self.follow_mode {
            FollowMode::Percentage => {
                self.fixed_follow_amount_sol = None; // 清除固定金额字段
            },
            FollowMode::FixedAmount => {
                self.follow_percentage = None; // 清除百分比字段
            }
        }
        self
    }
}