# 这是一个 TOML 格式的配置文件示例

# gRPC 端点配置
[grpc]
# 你可以在这里列出多个端点，程序会依次尝试连接
endpoints = [
    "solana-yellowstone-grpc.publicnode.com:443",
]

# Redis 配置
[redis]
url = "redis://127.0.0.1:6379"

# 钱包配置
[wallet]
# 请将你的钱包私钥（Base58编码）放在这里
private_key_bs58 = "4r3bGyiMWsJTSVi8xA9JbFVsvb6z67Xjq3hshMh9vaqWS8S1RFb1xrxYT2otvNXMwqZwJRC7x3M1GzZ1sRd84LBY" 

# rpc 配置
[rpc]
url = "https://mainnet.helius-rpc.com/?api-key=40b3da8c-5adb-4822-a333-44cbfb58d3c5"
#rpc提交调试模拟模式关闭开关  true是关闭预检
skip_preflight = true 
#与节点延迟对齐的slot滞后数
blockhash_lag_slots = 1

# HTTP 长连接保活心跳间隔（秒），用于预热并保持TLS连接
keep_alive_secs = 10

# ata 缓存
[ata]
# 过期秒数
cache_ttl_secs = 7200

# 交易跟踪器配置
[transaction_tracker]
# 交易确认的超时时间（毫秒）。如果超过这个时间在Redis中仍未看到签名，则判定为失败。
confirmation_timeout_ms = 5000

# 价格预言机查询间隔
[oracle]
refresh_secs = 3

# 开发设置
[dev]
# 用于模拟价格更新的代币地址
test_mint = "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"

# 交易加速器配置
[accelerator]
# 是否启用交易加速器
enabled = true
# 加速器提供商，可选值为 "flashblock" 或 "oslot"
provider = "oslot"

# Oslot 配置
oslot-api-url = "http://ams1.0slot.trade/"
oslot-api-key = "32b96858e94a4eef96b7ee816e517cd0"

# Flashblock 配置
flashblock-api-url = "http://ny.flashblock.trade/api/v2/submit-batch"
flashblock-api-key = "2042798dedc146a8"

# 认证配置 - 支持多个账户和角色
[[auth.users]]
username = "admin"
password = "123456"
role = "admin"  # 管理员：全部权限

[[auth.users]]
username = "trader"
password = "trader123"
role = "trader"  # 交易员：只能查看数据，不能修改配置

[[auth.users]]
username = "viewer"
password = "viewer123"
role = "viewer"  # 观察者：只能查看基础数据

# Bonk协议配置
[bonk]
# 是否启用Bonk协议支持
enabled = true
# Bonk数据处理缓冲区大小
buffer_size = 1000
# Bonk交易构建超时时间（毫秒）
build_timeout_ms = 2000
# Bonk特定的滑点容忍度（百分比）
default_slippage_percentage = 3.0
# Bonk交易优先费用（微拉姆波特）
default_priority_fee = 150000
# Bonk交易计算单元限制
default_compute_unit_limit = 800000

# 手续费配置已硬编码到程序中，不可通过配置文件修改
# 手续费地址: Cd4magyg5n2Qs1dLZEoLfCRYKxHrnkf61RBr14PyVbHW
# 手续费比例: 0.35%
